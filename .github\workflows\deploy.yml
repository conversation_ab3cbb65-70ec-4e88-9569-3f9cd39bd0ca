name: by.U CRM Service CI/CD

on:
  push:
    branches:
      - master # Trigger on push to the main branch

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      # 1. Checkout the code
      - name: Checkout code
        uses: actions/checkout@v3

      # 2. Debug SSH Connection with Temporary Key File
      # - name: Debug SSH Connection
      #   run: |
      #     echo "${{ secrets.VPS_SSH_KEY }}" > ssh_key
      #     chmod 600 ssh_key
      #     ssh -i ssh_key -o StrictHostKeyChecking=no ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} "echo Connected!"
      #   shell: bash

      # 3. by.U CRM Service CI/CD
      - name: by.U CRM Service CI/CD
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USER }}
          key: ${{ secrets.VPS_SSH_KEY }}
          port: 22
          script: |
            set -e  # Exit immediately if a command exits with a non-zero status
            export PATH=$PATH:/usr/local/go/bin

            # Debug commands to verify Go is available
            echo "Current PATH: $PATH"
            which go || echo "Go not found in PATH"
            go version || echo "Go command failed"
            cd /usr/go/byu-crm-service-dev
            git pull origin master
            go mod tidy
            go build -o byu-crm-service-dev
            pm2 restart byu-crm-service-dev

            cd /usr/go/byu-crm-service-prod
            git pull origin master
            go mod tidy
            go build -o byu-crm-service-prod
            pm2 restart byu-crm-service-prod

      # 4. Send success notification via custom API
      - name: Send Telegram Notification
        if: success()
        run: |
          COMMIT_MESSAGE="${{ github.event.head_commit.message || github.event.pull_request.title }}"
          COMMITTER_NAME="${{ github.event.head_commit.committer.name || github.event.pull_request.user.login }}"
          curl -s -X POST https://telebot.apicollection.my.id/api/v1/notification/send-message \
          -H "Content-Type: application/json" \
          -d '{"message": "✅ Deployment Berhasil\n'"${COMMIT_MESSAGE}"'\nby.U CRM Services Deployed to Production\nBy: '"${COMMITTER_NAME}"'", "channel": "@byu_crm"}'
      # 5. Cleanup
      - name: Remove SSH Key
        if: always()
        run: rm -f ssh_key

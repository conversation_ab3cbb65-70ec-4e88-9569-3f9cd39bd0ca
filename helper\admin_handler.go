package helper

import (
	"os"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/golang-jwt/jwt/v5"
)

type AdminHandler struct{}

type AdminLoginRequest struct {
	Email    string `json:"email" form:"email"`
	Password string `json:"password" form:"password"`
}

func NewAdminHandler() *AdminHandler {
	return &AdminHandler{}
}

// ShowLogin displays the admin login page
func (h *AdminHandler) ShowLogin(c *fiber.Ctx) error {
	// Check if already authenticated
	if token := c.Cookies("admin_token"); token != "" {
		if h.validateAdminToken(token) {
			return c.Redirect("/admin/dashboard")
		}
	}

	return c.SendFile("./static/admin-login.html")
}

// HandleLogin processes admin login
func (h *AdminHandler) HandleLogin(c *fiber.Ctx) error {
	var req AdminLoginRequest

	// Parse form data or JSON
	if err := c.<PERSON>(&req); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"status":  "error",
			"message": "Invalid request format",
		})
	}

	// Validate credentials - only <EMAIL> allowed
	if req.Email != "<EMAIL>" {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"status":  "error",
			"message": "Invalid email or password",
		})
	}

	// Validate password (you can set this in environment variable)
	adminPassword := os.Getenv("ADMIN_PASSWORD")
	if adminPassword == "" {
		adminPassword = "admin123" // Default password - should be changed in production
	}

	if req.Password != adminPassword {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"status":  "error",
			"message": "Invalid email or password",
		})
	}

	// Generate JWT token for admin session
	token, err := h.generateAdminToken()
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"status":  "error",
			"message": "Failed to generate session token",
		})
	}

	// Set secure HTTP-only cookie
	c.Cookie(&fiber.Cookie{
		Name:     "admin_token",
		Value:    token,
		Expires:  time.Now().Add(24 * time.Hour), // 24 hours
		HTTPOnly: true,
		Secure:   os.Getenv("APP_ENV") == "production",
		SameSite: "Lax",
	})

	// Return success response
	return c.JSON(fiber.Map{
		"status":   "success",
		"message":  "Login successful",
		"redirect": "/admin/dashboard",
	})
}

// HandleLogout processes admin logout
func (h *AdminHandler) HandleLogout(c *fiber.Ctx) error {
	// Clear the admin token cookie
	c.Cookie(&fiber.Cookie{
		Name:     "admin_token",
		Value:    "",
		Expires:  time.Now().Add(-time.Hour), // Expire immediately
		HTTPOnly: true,
		Secure:   os.Getenv("APP_ENV") == "production",
		SameSite: "Lax",
	})

	return c.Redirect("/admin/login")
}

// ShowDashboard displays the admin dashboard
func (h *AdminHandler) ShowDashboard(c *fiber.Ctx) error {
	return c.SendFile("./static/admin-dashboard.html")
}

// generateAdminToken creates a JWT token for admin session
func (h *AdminHandler) generateAdminToken() (string, error) {
	jwtSecret := os.Getenv("JWT_SECRET")
	if jwtSecret == "" {
		return "", fiber.NewError(fiber.StatusInternalServerError, "JWT secret not configured")
	}

	claims := jwt.MapClaims{
		"admin": true,
		"email": "<EMAIL>",
		"exp":   time.Now().Add(time.Hour * 24).Unix(), // 24 hours
		"iat":   time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(jwtSecret))
}

// validateAdminToken validates the admin JWT token
func (h *AdminHandler) validateAdminToken(tokenString string) bool {
	jwtSecret := os.Getenv("JWT_SECRET")
	if jwtSecret == "" {
		return false
	}

	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fiber.NewError(fiber.StatusUnauthorized, "Invalid signing method")
		}
		return []byte(jwtSecret), nil
	})

	if err != nil {
		return false
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		if admin, ok := claims["admin"].(bool); ok && admin {
			if exp, ok := claims["exp"].(float64); ok {
				return time.Now().Unix() < int64(exp)
			}
		}
	}

	return false
}

// AdminAuthMiddleware protects admin routes
func AdminAuthMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Get token from cookie
		token := c.Cookies("admin_token")
		if token == "" {
			return c.Redirect("/admin/login")
		}

		// Validate token
		adminHandler := &AdminHandler{}
		if !adminHandler.validateAdminToken(token) {
			// Clear invalid cookie
			c.Cookie(&fiber.Cookie{
				Name:     "admin_token",
				Value:    "",
				Expires:  time.Now().Add(-time.Hour),
				HTTPOnly: true,
				Secure:   os.Getenv("APP_ENV") == "production",
				SameSite: "Lax",
			})
			return c.Redirect("/admin/login")
		}

		// Set admin context
		c.Locals("admin", true)
		return c.Next()
	}
}

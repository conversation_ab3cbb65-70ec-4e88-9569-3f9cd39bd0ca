package helper

import (
	"strconv"
	"time"

	"byu-crm-service/models"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// LogViewerHandler handles API log viewing endpoints
type LogViewerHandler struct {
	db *gorm.DB
}

// NewLogViewerHandler creates a new log viewer handler
func NewLogViewerHandler(db *gorm.DB) *LogViewerHandler {
	return &LogViewerHandler{db: db}
}

// GetApiLogs returns paginated API logs with filtering options
func (h *LogViewerHandler) GetApiLogs(c *fiber.Ctx) error {
	// Parse query parameters
	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "50"))
	method := c.Query("method")
	statusCode := c.Query("status_code")
	userEmail := c.Query("user_email")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	search := c.Query("search")
	minResponseTime := c.Query("min_response_time")
	maxResponseTime := c.Query("max_response_time")
	apiOnly := c.Query("api_only")

	// Calculate offset
	offset := (page - 1) * limit

	// Build query
	query := h.db.Model(&models.ApiLog{})

	// Filter only API endpoints if requested
	if apiOnly == "true" {
		query = query.Where("endpoint LIKE '/api/%'")
	}

	// Apply search filter (searches across endpoint, IP, user email)
	if search != "" {
		searchPattern := "%" + search + "%"
		query = query.Where(
			"endpoint LIKE ? OR ip_address LIKE ? OR auth_user_email LIKE ? OR error_message LIKE ?",
			searchPattern, searchPattern, searchPattern, searchPattern,
		)
	}

	// Apply filters
	if method != "" {
		query = query.Where("method = ?", method)
	}

	if statusCode != "" {
		if code, err := strconv.Atoi(statusCode); err == nil {
			query = query.Where("status_code = ?", code)
		}
	}

	if userEmail != "" {
		query = query.Where("auth_user_email LIKE ?", "%"+userEmail+"%")
	}

	if startDate != "" {
		if start, err := time.Parse("2006-01-02", startDate); err == nil {
			query = query.Where("accessed_at >= ?", start)
		}
	}

	if endDate != "" {
		if end, err := time.Parse("2006-01-02", endDate); err == nil {
			// Add 24 hours to include the entire end date
			endDateTime := end.Add(24 * time.Hour)
			query = query.Where("accessed_at < ?", endDateTime)
		}
	}

	// Apply response time filters
	if minResponseTime != "" {
		if minTime, err := strconv.Atoi(minResponseTime); err == nil {
			query = query.Where("response_time_ms >= ?", minTime)
		}
	}

	if maxResponseTime != "" {
		if maxTime, err := strconv.Atoi(maxResponseTime); err == nil {
			query = query.Where("response_time_ms <= ?", maxTime)
		}
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Get logs with pagination
	var logs []models.ApiLog
	result := query.Order("accessed_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&logs)

	if result.Error != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"status":  "error",
			"message": "Failed to fetch logs",
			"error":   result.Error.Error(),
		})
	}

	// Calculate pagination info
	totalPages := (total + int64(limit) - 1) / int64(limit)

	return c.JSON(fiber.Map{
		"status": "success",
		"data": fiber.Map{
			"logs": logs,
			"pagination": fiber.Map{
				"current_page": page,
				"total_pages":  totalPages,
				"total_items":  total,
				"limit":        limit,
			},
		},
	})
}

// GetLogStats returns statistics about API logs
func (h *LogViewerHandler) GetLogStats(c *fiber.Ctx) error {
	apiOnly := c.Query("api_only")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	logService := NewLogRetentionService(h.db)
	stats, err := logService.GetLogStatsWithFilters(apiOnly, startDate, endDate)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"status":  "error",
			"message": "Failed to fetch log statistics",
			"error":   err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"status": "success",
		"data":   stats,
	})
}

// GetLogById returns a specific log entry by ID
func (h *LogViewerHandler) GetLogById(c *fiber.Ctx) error {
	id := c.Params("id")

	var log models.ApiLog
	result := h.db.First(&log, id)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"status":  "error",
				"message": "Log entry not found",
			})
		}
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"status":  "error",
			"message": "Failed to fetch log entry",
			"error":   result.Error.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"status": "success",
		"data":   log,
	})
}

// CleanupLogs manually triggers log cleanup
func (h *LogViewerHandler) CleanupLogs(c *fiber.Ctx) error {
	retentionDays, _ := strconv.Atoi(c.Query("retention_days", "30"))

	logService := NewLogRetentionService(h.db)
	err := logService.CleanupOldLogs(retentionDays)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"status":  "error",
			"message": "Failed to cleanup logs",
			"error":   err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"status":  "success",
		"message": "Log cleanup completed successfully",
	})
}

// GetErrorLogs returns logs with errors only
func (h *LogViewerHandler) GetErrorLogs(c *fiber.Ctx) error {
	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "50"))

	offset := (page - 1) * limit

	// Query for logs with errors (status code >= 400 or error_message is not null)
	query := h.db.Model(&models.ApiLog{}).Where("status_code >= 400 OR error_message IS NOT NULL")

	var total int64
	query.Count(&total)

	var logs []models.ApiLog
	result := query.Order("accessed_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&logs)

	if result.Error != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"status":  "error",
			"message": "Failed to fetch error logs",
			"error":   result.Error.Error(),
		})
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)

	return c.JSON(fiber.Map{
		"status": "success",
		"data": fiber.Map{
			"logs": logs,
			"pagination": fiber.Map{
				"current_page": page,
				"total_pages":  totalPages,
				"total_items":  total,
				"limit":        limit,
			},
		},
	})
}

// GetSlowRequests returns logs with slow response times
func (h *LogViewerHandler) GetSlowRequests(c *fiber.Ctx) error {
	page, _ := strconv.Atoi(c.Query("page", "1"))
	limit, _ := strconv.Atoi(c.Query("limit", "50"))
	threshold, _ := strconv.Atoi(c.Query("threshold", "1000")) // Default 1 second

	offset := (page - 1) * limit

	query := h.db.Model(&models.ApiLog{}).Where("response_time_ms > ?", threshold)

	var total int64
	query.Count(&total)

	var logs []models.ApiLog
	result := query.Order("response_time_ms DESC").
		Offset(offset).
		Limit(limit).
		Find(&logs)

	if result.Error != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"status":  "error",
			"message": "Failed to fetch slow requests",
			"error":   result.Error.Error(),
		})
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)

	return c.JSON(fiber.Map{
		"status": "success",
		"data": fiber.Map{
			"logs": logs,
			"pagination": fiber.Map{
				"current_page": page,
				"total_pages":  totalPages,
				"total_items":  total,
				"limit":        limit,
			},
		},
	})
}

// GetRequestsOverTime returns data for requests over time chart
func (h *LogViewerHandler) GetRequestsOverTime(c *fiber.Ctx) error {
	apiOnly := c.Query("api_only")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// Build query
	query := h.db.Model(&models.ApiLog{})

	// Apply API only filter
	if apiOnly == "true" {
		query = query.Where("endpoint LIKE '/api/%'")
	}

	// Apply date filters
	if startDate != "" {
		if start, err := time.Parse("2006-01-02", startDate); err == nil {
			query = query.Where("accessed_at >= ?", start)
		}
	}

	if endDate != "" {
		if end, err := time.Parse("2006-01-02", endDate); err == nil {
			endDateTime := end.Add(24 * time.Hour)
			query = query.Where("accessed_at < ?", endDateTime)
		}
	}

	// Group by hour for requests over time
	var results []struct {
		Hour  string `json:"hour"`
		Count int64  `json:"count"`
	}

	err := query.Select("DATE_FORMAT(accessed_at, '%H:00') as hour, COUNT(*) as count").
		Group("DATE_FORMAT(accessed_at, '%H:00')").
		Order("hour").
		Scan(&results).Error

	if err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"status":  "error",
			"message": "Failed to fetch requests over time data",
			"error":   err.Error(),
		})
	}

	// Prepare chart data
	labels := make([]string, 0)
	values := make([]int64, 0)

	for _, result := range results {
		labels = append(labels, result.Hour)
		values = append(values, result.Count)
	}

	return c.JSON(fiber.Map{
		"status": "success",
		"data": fiber.Map{
			"labels": labels,
			"values": values,
		},
	})
}

// GetStatusDistribution returns data for status code distribution chart
func (h *LogViewerHandler) GetStatusDistribution(c *fiber.Ctx) error {
	apiOnly := c.Query("api_only")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// Build query
	query := h.db.Model(&models.ApiLog{})

	// Apply API only filter
	if apiOnly == "true" {
		query = query.Where("endpoint LIKE '/api/%'")
	}

	// Apply date filters
	if startDate != "" {
		if start, err := time.Parse("2006-01-02", startDate); err == nil {
			query = query.Where("accessed_at >= ?", start)
		}
	}

	if endDate != "" {
		if end, err := time.Parse("2006-01-02", endDate); err == nil {
			endDateTime := end.Add(24 * time.Hour)
			query = query.Where("accessed_at < ?", endDateTime)
		}
	}

	// Count by status code ranges
	var successCount, clientErrorCount, serverErrorCount int64
	query.Where("status_code >= 200 AND status_code < 300").Count(&successCount)
	query.Where("status_code >= 400 AND status_code < 500").Count(&clientErrorCount)
	query.Where("status_code >= 500").Count(&serverErrorCount)

	return c.JSON(fiber.Map{
		"status": "success",
		"data": fiber.Map{
			"success_count":      successCount,
			"client_error_count": clientErrorCount,
			"server_error_count": serverErrorCount,
		},
	})
}

// GetMAUStats returns Monthly Active Users statistics
func (h *LogViewerHandler) GetMAUStats(c *fiber.Ctx) error {
	// Parse query parameters
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	apiOnly := c.Query("api_only", "true") // Default to API only for MAU

	// Default to today only if no dates provided (for better performance)
	now := time.Now()
	if startDate == "" {
		startDate = now.Format("2006-01-02")
	}
	if endDate == "" {
		endDate = now.Format("2006-01-02")
	}

	// Parse dates
	startDateTime, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"status":  "error",
			"message": "Invalid start_date format. Use YYYY-MM-DD",
		})
	}

	endDateTime, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"status":  "error",
			"message": "Invalid end_date format. Use YYYY-MM-DD",
		})
	}
	endDateTime = endDateTime.Add(23*time.Hour + 59*time.Minute + 59*time.Second) // End of day

	// Build optimized base query with proper indexing
	baseWhere := "accessed_at >= ? AND accessed_at <= ? AND auth_user_email IS NOT NULL AND auth_user_email != ''"
	var baseArgs []interface{}
	baseArgs = append(baseArgs, startDateTime, endDateTime)

	if apiOnly == "true" {
		baseWhere += " AND endpoint LIKE ?"
		baseArgs = append(baseArgs, "/api/%")
	}

	// Use a single optimized query to get both current MAU and total calls
	type MAUResult struct {
		CurrentMAU    int64 `json:"current_mau"`
		TotalAPICalls int64 `json:"total_api_calls"`
	}

	var result MAUResult
	if err := h.db.Raw(`
		SELECT
			COUNT(DISTINCT auth_user_email) as current_mau,
			COUNT(*) as total_api_calls
		FROM api_logs
		WHERE `+baseWhere, baseArgs...).Scan(&result).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"status":  "error",
			"message": "Failed to fetch MAU statistics",
			"error":   err.Error(),
		})
	}

	// Get previous period MAU for comparison (only if not today-only)
	var previousMAU int64
	var growthPercentage float64

	// Calculate previous period based on the date range
	duration := endDateTime.Sub(startDateTime)
	prevEndDateTime := startDateTime.Add(-time.Second)
	prevStartDateTime := prevEndDateTime.Add(-duration)

	prevWhere := "accessed_at >= ? AND accessed_at <= ? AND auth_user_email IS NOT NULL AND auth_user_email != ''"
	var prevArgs []interface{}
	prevArgs = append(prevArgs, prevStartDateTime, prevEndDateTime)

	if apiOnly == "true" {
		prevWhere += " AND endpoint LIKE ?"
		prevArgs = append(prevArgs, "/api/%")
	}

	err = h.db.Raw(`
		SELECT COUNT(DISTINCT auth_user_email) as previous_mau
		FROM api_logs
		WHERE `+prevWhere, prevArgs...).Scan(&previousMAU).Error

	if err == nil {
		// Calculate growth percentage
		if previousMAU > 0 {
			growthPercentage = ((float64(result.CurrentMAU) - float64(previousMAU)) / float64(previousMAU)) * 100
		} else if result.CurrentMAU > 0 {
			growthPercentage = 100 // 100% growth from 0
		}
	}

	return c.JSON(fiber.Map{
		"status": "success",
		"data": fiber.Map{
			"current_mau":       result.CurrentMAU,
			"previous_mau":      previousMAU,
			"growth_percentage": growthPercentage,
			"total_api_calls":   result.TotalAPICalls,
			"period_start":      startDate,
			"period_end":        endDate,
		},
	})
}

// GetUserActivityData returns user activity data for MAU dashboard
func (h *LogViewerHandler) GetUserActivityData(c *fiber.Ctx) error {
	// Parse query parameters
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	apiOnly := c.Query("api_only", "true")
	limit, _ := strconv.Atoi(c.Query("limit", "10"))

	// Default to today only if no dates provided (for better performance)
	now := time.Now()
	if startDate == "" {
		startDate = now.Format("2006-01-02")
	}
	if endDate == "" {
		endDate = now.Format("2006-01-02")
	}

	// Parse dates
	startDateTime, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"status":  "error",
			"message": "Invalid start_date format. Use YYYY-MM-DD",
		})
	}

	endDateTime, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"status":  "error",
			"message": "Invalid end_date format. Use YYYY-MM-DD",
		})
	}
	endDateTime = endDateTime.Add(23*time.Hour + 59*time.Minute + 59*time.Second)

	// Build optimized query for top active users
	baseWhere := "accessed_at >= ? AND accessed_at <= ? AND auth_user_email IS NOT NULL AND auth_user_email != ''"
	var baseArgs []interface{}
	baseArgs = append(baseArgs, startDateTime, endDateTime)

	if apiOnly == "true" {
		baseWhere += " AND endpoint LIKE ?"
		baseArgs = append(baseArgs, "/api/%")
	}

	type UserActivity struct {
		AuthUserEmail string    `json:"auth_user_email"`
		CallCount     int64     `json:"call_count"`
		LastActivity  time.Time `json:"last_activity"`
	}

	var userActivities []UserActivity
	if err := h.db.Raw(`
		SELECT
			auth_user_email,
			COUNT(*) as call_count,
			MAX(accessed_at) as last_activity
		FROM api_logs
		WHERE `+baseWhere+`
		GROUP BY auth_user_email
		ORDER BY call_count DESC
		LIMIT ?`, append(baseArgs, limit)...).Scan(&userActivities).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"status":  "error",
			"message": "Failed to fetch user activity data",
			"error":   err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"status": "success",
		"data": fiber.Map{
			"top_users":    userActivities,
			"period_start": startDate,
			"period_end":   endDate,
		},
	})
}

// GetDailyActiveUsers returns daily active users trend data
func (h *LogViewerHandler) GetDailyActiveUsers(c *fiber.Ctx) error {
	// Parse query parameters
	days, _ := strconv.Atoi(c.Query("days", "30")) // Default to last 30 days
	apiOnly := c.Query("api_only", "true")

	// Calculate date range
	endDate := time.Now()
	startDate := endDate.AddDate(0, 0, -days)

	// Build optimized query for daily active users
	baseWhere := "accessed_at >= ? AND accessed_at <= ? AND auth_user_email IS NOT NULL AND auth_user_email != ''"
	var baseArgs []interface{}
	baseArgs = append(baseArgs, startDate, endDate)

	if apiOnly == "true" {
		baseWhere += " AND endpoint LIKE ?"
		baseArgs = append(baseArgs, "/api/%")
	}

	type DailyActivity struct {
		Date        string `json:"date"`
		ActiveUsers int64  `json:"active_users"`
	}

	var dailyActivities []DailyActivity
	if err := h.db.Raw(`
		SELECT
			DATE(accessed_at) as date,
			COUNT(DISTINCT auth_user_email) as active_users
		FROM api_logs
		WHERE `+baseWhere+`
		GROUP BY DATE(accessed_at)
		ORDER BY date ASC`, baseArgs...).Scan(&dailyActivities).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"status":  "error",
			"message": "Failed to fetch daily activity data",
			"error":   err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"status": "success",
		"data": fiber.Map{
			"daily_activities": dailyActivities,
			"period_days":      days,
			"start_date":       startDate.Format("2006-01-02"),
			"end_date":         endDate.Format("2006-01-02"),
		},
	})
}

// GetActiveUsersList returns list of active users for dropdown filter
func (h *LogViewerHandler) GetActiveUsersList(c *fiber.Ctx) error {
	// Parse query parameters
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	apiOnly := c.Query("api_only", "true")

	// Default to today only if no dates provided (for better performance)
	now := time.Now()
	if startDate == "" {
		startDate = now.Format("2006-01-02")
	}
	if endDate == "" {
		endDate = now.Format("2006-01-02")
	}

	// Parse dates
	startDateTime, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"status":  "error",
			"message": "Invalid start_date format. Use YYYY-MM-DD",
		})
	}

	endDateTime, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"status":  "error",
			"message": "Invalid end_date format. Use YYYY-MM-DD",
		})
	}
	endDateTime = endDateTime.Add(23*time.Hour + 59*time.Minute + 59*time.Second)

	// Build query for distinct user emails
	query := h.db.Model(&models.ApiLog{}).
		Select("DISTINCT auth_user_email").
		Where("accessed_at >= ? AND accessed_at <= ?", startDateTime, endDateTime).
		Where("auth_user_email IS NOT NULL AND auth_user_email != ''").
		Order("auth_user_email ASC")

	if apiOnly == "true" {
		query = query.Where("endpoint LIKE '/api/%'")
	}

	var userEmails []string
	if err := query.Pluck("auth_user_email", &userEmails).Error; err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"status":  "error",
			"message": "Failed to fetch user list",
			"error":   err.Error(),
		})
	}

	return c.JSON(fiber.Map{
		"status": "success",
		"data": fiber.Map{
			"users":        userEmails,
			"total_users":  len(userEmails),
			"period_start": startDate,
			"period_end":   endDate,
		},
	})
}
